#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP Enhanced Agent - 基于Agent-KB架构的智能统计查询代理
集成微代理系统、知识库和内存管理，提供智能化的MCP服务器增强功能
"""

import json
import os
import asyncio
import hashlib
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from dataclasses import asdict

from mcp.server.fastmcp import FastMCP

# 导入Agent-KB核心组件
try:
    from openhands.microagent import (
        BaseMicroAgent, 
        KnowledgeMicroAgent, 
        RepoMicroAgent,
        load_microagents_from_dir
    )
    from openhands.memory.memory import Memory
    from openhands.events.stream import EventStream
    from openhands.events.action.agent import RecallAction
    from openhands.events.event import EventSource, RecallType
except ImportError:
    print("Warning: Agent-KB components not available, using fallback implementations")
    BaseMicroAgent = object
    KnowledgeMicroAgent = object
    RepoMicroAgent = object

# 导入原始MCP服务器功能
from stats_mcp_server_official import StatsApiClient, api_client

# 导入增强组件
from enhanced_query_parser import EnhancedQueryParser
from knowledge_management_system import KnowledgeManagementSystem
from knowledge_microagent import KnowledgeMicroAgent
from knowledge_generation_system import KnowledgeGenerationSystem
from gemini_model_integration import initialize_gemini_client, get_gemini_client
from qwen3_model_integration import initialize_qwen3_client, get_qwen3_client
from config_manager import get_config

class MCPEnhancedAgent:
    """
    MCP增强代理 - 集成Agent-KB架构的智能统计查询系统
    """
    
    def __init__(self, config_dir: str = None, env_file: str = ".env"):
        """
        初始化MCP增强代理

        Args:
            config_dir: 配置目录路径（可选，将从配置文件读取）
            env_file: 环境配置文件路径
        """
        # 加载配置
        self.config = get_config(env_file)
        self.config.setup_logging()

        # 设置目录路径
        if config_dir is None:
            config_dir = self.config.knowledge.microagents_config_dir

        self.config_dir = Path(config_dir)
        self.microagents_dir = self.config_dir / "microagents"
        self.knowledge_dir = self.config_dir / "knowledge_base"

        # 确保目录存在
        self._ensure_directories()

        # 初始化组件
        self.knowledge_microagents: Dict[str, KnowledgeMicroAgent] = {}
        self.repo_microagents: Dict[str, RepoMicroAgent] = {}
        self.query_patterns: Dict[str, Any] = {}
        self.api_mappings: Dict[str, Any] = {}
        self.query_cache: Dict[str, Any] = {}

        # 加载配置和微代理
        self._load_configurations()
        self._load_microagents()

        # 初始化知识管理系统
        self.knowledge_system = KnowledgeManagementSystem(str(self.config_dir))

        # 初始化知识生成系统
        self.knowledge_generation = KnowledgeGenerationSystem(
            self.config.knowledge.storage_dir
        )

        # 初始化模型客户端
        self._init_model_clients()

        # 初始化增强查询解析器
        self.query_parser = EnhancedQueryParser(self)

        # 初始化FastMCP服务器
        self.mcp = FastMCP(self.config.server.name)
        self._register_mcp_tools()

        # 打印配置摘要
        if not self.config.is_mock_mode():
            self.config.print_config_summary()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.config_dir,
            self.microagents_dir,
            self.microagents_dir / "knowledge",
            self.microagents_dir / "tasks",
            self.knowledge_dir,
            self.knowledge_dir / "query_patterns",
            self.knowledge_dir / "api_mappings",
            self.knowledge_dir / "learned_patterns"
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    def _init_model_clients(self):
        """初始化模型客户端"""
        try:
            # 初始化Gemini客户端
            self.gemini_client = initialize_gemini_client(
                api_key=self.config.gemini.api_key,
                model_name=self.config.gemini.model_name
            )

            # 初始化Qwen3客户端
            self.qwen3_client = initialize_qwen3_client(
                api_key=self.config.qwen3.api_key,
                base_url=self.config.qwen3.base_url,
                model_name=self.config.qwen3.model_name
            )

            print(f"模型客户端初始化完成:")
            print(f"  Gemini: {'可用' if self.gemini_client.available else '不可用'}")
            print(f"  Qwen3: {'可用' if self.qwen3_client.available else '不可用'}")

            # 如果是模拟模式，显示提示
            if self.config.is_mock_mode():
                print("  注意: 当前运行在模拟模式下")

        except Exception as e:
            print(f"模型客户端初始化失败: {e}")
            self.gemini_client = None
            self.qwen3_client = None
    
    def _load_configurations(self):
        """加载配置文件"""
        # 加载查询模式
        patterns_dir = self.knowledge_dir / "query_patterns"
        for pattern_file in patterns_dir.glob("*.json"):
            with open(pattern_file, 'r', encoding='utf-8') as f:
                patterns = json.load(f)
                self.query_patterns.update(patterns)
        
        # 加载API映射
        mappings_dir = self.knowledge_dir / "api_mappings"
        for mapping_file in mappings_dir.glob("*.json"):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mappings = json.load(f)
                self.api_mappings.update(mappings)
    
    def _load_microagents(self):
        """加载微代理"""
        try:
            # 使用我们自己的KnowledgeMicroAgent实现
            self._load_knowledge_microagents()
            print(f"加载了 {len(self.knowledge_microagents)} 个知识微代理")
        except Exception as e:
            print(f"加载微代理失败: {e}")
            self._load_simple_microagents()

    def _load_knowledge_microagents(self):
        """加载知识微代理"""
        knowledge_dir = self.microagents_dir / "knowledge"

        if not knowledge_dir.exists():
            print("知识微代理目录不存在，使用简化版本")
            self._load_simple_microagents()
            return

        self.knowledge_microagents = {}

        # 加载所有.md文件作为知识微代理
        for md_file in knowledge_dir.glob("*.md"):
            try:
                microagent = KnowledgeMicroAgent.load_from_file(md_file, self)
                self.knowledge_microagents[microagent.name] = microagent
                print(f"  加载微代理: {microagent.name}")
            except Exception as e:
                print(f"  加载微代理失败 {md_file.name}: {e}")

        # 如果没有加载到任何微代理，使用简化版本
        if not self.knowledge_microagents:
            print("未找到有效的知识微代理文件，使用简化版本")
            self._load_simple_microagents()
    
    def _load_simple_microagents(self):
        """简化版微代理加载（当Agent-KB不可用时）"""
        # 创建默认的统计查询微代理
        self.knowledge_microagents = {
            "stats_query": SimpleKnowledgeAgent(
                name="stats_query",
                triggers=["链路", "统计", "查询", "概要", "流量"],
                patterns=["link_summary", "daily_stats", "traffic_analysis"]
            ),
            "network_analysis": SimpleKnowledgeAgent(
                name="network_analysis", 
                triggers=["应用", "id", "配置", "网络"],
                patterns=["app_query", "config_query", "network_info"]
            ),
            "ip_analysis": SimpleKnowledgeAgent(
                name="ip_analysis",
                triggers=["ip", "地址", "top", "排序"],
                patterns=["ip_top", "ip_stats", "address_analysis"]
            )
        }
    
    def _register_mcp_tools(self):
        """注册MCP工具"""
        
        @self.mcp.tool()
        def intelligent_query(query: str) -> str:
            """
            智能统计查询 - 使用自然语言进行统计数据查询
            
            Args:
                query: 自然语言查询描述，例如：
                      - "查询链路1下今天的概要统计"
                      - "查询下当前有哪些链路"
                      - "查询下应用xxxx的id是多少"
                      - "查询下链路2下今天的ip地址Top20"
            
            Returns:
                查询结果的JSON字符串
            """
            return self.process_intelligent_query(query)
        
        @self.mcp.tool()
        def learn_query_pattern(query: str, result: str, feedback: str = "") -> str:
            """
            学习查询模式 - 将成功的查询模式保存到知识库
            
            Args:
                query: 原始查询
                result: 查询结果
                feedback: 用户反馈（可选）
            
            Returns:
                学习结果
            """
            return self.learn_pattern(query, result, feedback)
        
        @self.mcp.tool()
        def get_available_patterns() -> str:
            """
            获取可用的查询模式

            Returns:
                可用查询模式的JSON字符串
            """
            return json.dumps(self.query_patterns, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        def get_knowledge_summary() -> str:
            """
            获取知识库摘要信息

            Returns:
                知识库摘要的JSON字符串
            """
            summary = self.knowledge_system.get_knowledge_summary()
            return json.dumps(summary, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        def get_query_suggestions(partial_query: str) -> str:
            """
            获取查询建议

            Args:
                partial_query: 部分查询内容

            Returns:
                查询建议列表的JSON字符串
            """
            suggestions = self.query_parser.get_query_suggestions(partial_query)
            return json.dumps({"suggestions": suggestions}, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        def generate_knowledge_with_gemini(query: str, context: str = "{}") -> str:
            """
            使用Gemini生成知识模式

            Args:
                query: 触发生成的查询
                context: 上下文信息（JSON字符串）

            Returns:
                知识生成结果的JSON字符串
            """
            import asyncio
            try:
                context_dict = json.loads(context) if context != "{}" else {}
                result = asyncio.run(self.knowledge_generation.generate_knowledge_with_gemini(query, context_dict))
                return json.dumps(asdict(result), ensure_ascii=False, indent=2, default=str)
            except Exception as e:
                return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        def reuse_knowledge_with_qwen3(query: str, context: str = "{}") -> str:
            """
            使用Qwen3复用已学习的知识

            Args:
                query: 用户查询
                context: 上下文信息（JSON字符串）

            Returns:
                知识复用结果的JSON字符串
            """
            import asyncio
            try:
                context_dict = json.loads(context) if context != "{}" else {}
                result = asyncio.run(self.knowledge_generation.reuse_knowledge_with_qwen3(query, context_dict))
                return json.dumps(result, ensure_ascii=False, indent=2)
            except Exception as e:
                return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        def get_model_statistics() -> str:
            """
            获取模型使用统计

            Returns:
                模型统计信息的JSON字符串
            """
            stats = {
                "knowledge_generation": self.knowledge_generation.get_statistics(),
                "gemini": self.gemini_client.get_statistics() if self.gemini_client else {},
                "qwen3": self.qwen3_client.get_statistics() if self.qwen3_client else {}
            }
            return json.dumps(stats, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        def get_microagent_statistics() -> str:
            """
            获取微代理统计信息

            Returns:
                微代理统计信息的JSON字符串
            """
            stats = {}
            for name, agent in self.knowledge_microagents.items():
                if hasattr(agent, 'get_statistics'):
                    stats[name] = agent.get_statistics()
                else:
                    stats[name] = {
                        "name": name,
                        "type": "simple_agent",
                        "triggers": getattr(agent, 'triggers', [])
                    }
            return json.dumps(stats, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        def get_configuration() -> str:
            """
            获取当前配置信息

            Returns:
                配置信息的JSON字符串
            """
            config_info = {
                "server": {
                    "name": self.config.server.name,
                    "host": self.config.server.host,
                    "port": self.config.server.port,
                    "https_enabled": self.config.server.enable_https
                },
                "models": {
                    "gemini": {
                        "model_name": self.config.gemini.model_name,
                        "available": bool(self.config.gemini.api_key),
                        "base_url": self.config.gemini.base_url
                    },
                    "qwen3": {
                        "model_name": self.config.qwen3.model_name,
                        "available": bool(self.config.qwen3.api_key),
                        "base_url": self.config.qwen3.base_url
                    }
                },
                "knowledge": {
                    "storage_dir": self.config.knowledge.storage_dir,
                    "microagents_config_dir": self.config.knowledge.microagents_config_dir,
                    "cache_max_size": self.config.knowledge.cache_max_size,
                    "cache_ttl_hours": self.config.knowledge.cache_ttl_hours
                },
                "performance": {
                    "query_timeout": self.config.performance.query_timeout,
                    "max_concurrent_queries": self.config.performance.max_concurrent_queries,
                    "performance_monitoring": self.config.performance.enable_performance_monitoring
                },
                "debug": {
                    "debug_mode": self.config.is_debug_mode(),
                    "mock_mode": self.config.is_mock_mode(),
                    "log_level": self.config.logging.level
                }
            }
            return json.dumps(config_info, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        def reload_configuration() -> str:
            """
            重新加载配置

            Returns:
                重新加载结果的JSON字符串
            """
            try:
                from config_manager import reload_config
                self.config = reload_config()
                self.config.setup_logging()

                # 重新初始化模型客户端
                self._init_model_clients()

                return json.dumps({
                    "success": True,
                    "message": "配置重新加载成功",
                    "timestamp": datetime.now().isoformat()
                }, ensure_ascii=False, indent=2)

            except Exception as e:
                return json.dumps({
                    "success": False,
                    "error": str(e),
                    "message": "配置重新加载失败"
                }, ensure_ascii=False, indent=2)
    
    async def process_intelligent_query(self, query: str) -> str:
        """
        处理智能查询 - 使用完整的知识生成和复用系统

        Args:
            query: 用户的自然语言查询

        Returns:
            查询结果的JSON字符串
        """
        start_time = time.time()

        try:
            # 1. 触发相关微代理
            triggered_agents = self._trigger_microagents(query)

            # 2. 检查是否有微代理可以直接处理
            microagent_result = await self._try_microagent_processing(query, triggered_agents)
            if microagent_result and microagent_result.get("success"):
                microagent_result["meta"] = {
                    "strategy": "microagent_direct",
                    "triggered_agents": triggered_agents,
                    "response_time": time.time() - start_time
                }
                return json.dumps(microagent_result, ensure_ascii=False, indent=2)

            # 3. 使用知识生成系统进行智能处理
            context = {
                "triggered_agents": triggered_agents,
                "microagent_result": microagent_result
            }

            knowledge_result = await self.knowledge_generation.intelligent_query_processing(
                query, context
            )

            if knowledge_result["success"]:
                # 4. 如果知识系统成功，执行实际的API调用
                if "result" in knowledge_result and isinstance(knowledge_result["result"], str):
                    try:
                        # 尝试解析知识系统返回的API调用参数
                        api_params = json.loads(knowledge_result["result"])
                        if "api_call" in api_params:
                            # 执行API调用
                            api_result = self._execute_api_call(api_params["api_call"])
                            knowledge_result["api_result"] = api_result
                    except:
                        pass

                # 5. 添加元信息
                knowledge_result["meta"] = {
                    "triggered_agents": triggered_agents,
                    "response_time": time.time() - start_time,
                    "total_time": knowledge_result.get("generation_time", 0) + knowledge_result.get("processing_time", 0)
                }

                return json.dumps(knowledge_result, ensure_ascii=False, indent=2)

            # 6. 如果知识系统也失败，使用传统方法
            fallback_result = await self._fallback_processing(query, triggered_agents)
            fallback_result["meta"] = {
                "strategy": "fallback",
                "triggered_agents": triggered_agents,
                "response_time": time.time() - start_time,
                "knowledge_error": knowledge_result.get("error", "未知错误")
            }

            return json.dumps(fallback_result, ensure_ascii=False, indent=2)

        except Exception as e:
            response_time = time.time() - start_time

            return json.dumps({
                "success": False,
                "error": str(e),
                "message": "查询处理失败",
                "meta": {
                    "strategy": "error",
                    "response_time": response_time
                }
            }, ensure_ascii=False, indent=2)
    
    def _trigger_microagents(self, query: str) -> List[str]:
        """触发相关的微代理"""
        triggered = []
        query_lower = query.lower()

        for name, agent in self.knowledge_microagents.items():
            if hasattr(agent, 'triggers'):
                triggers = agent.triggers
            else:
                triggers = getattr(agent, 'triggers', [])

            for trigger in triggers:
                if trigger.lower() in query_lower:
                    triggered.append(name)
                    break

        return triggered

    def _use_learned_pattern(self, query: str, pattern) -> Dict[str, Any]:
        """使用学习到的模式处理查询"""
        # 更新模式使用统计
        pattern.usage_count += 1
        pattern.last_used = datetime.now()

        # 基于学习到的模式构建查询
        parsed_query = {
            "type": pattern.intent_type,
            "parameters": pattern.parameters.copy()
        }

        # 提取当前查询中的动态参数
        current_entities = self.query_parser._extract_entities(query, pattern.intent_type)

        # 更新参数
        if "netlink_id" in current_entities:
            parsed_query["parameters"]["netlink"] = current_entities["netlink_id"]

        if "time_range" in current_entities:
            time_range = current_entities["time_range"]
            parsed_query["parameters"]["begintime"] = time_range["begin"]
            parsed_query["parameters"]["endtime"] = time_range["end"]

        if "topcount" in current_entities:
            parsed_query["parameters"]["topcount"] = current_entities["topcount"]

        return parsed_query

    async def _try_microagent_processing(self, query: str, triggered_agents: List[str]) -> Optional[Dict[str, Any]]:
        """尝试使用微代理直接处理查询"""
        if not triggered_agents:
            return None

        # 选择最合适的微代理
        best_agent_name = triggered_agents[0]  # 简单选择第一个

        if best_agent_name in self.knowledge_microagents:
            microagent = self.knowledge_microagents[best_agent_name]
            try:
                result = microagent.process_query(query)
                return result
            except Exception as e:
                print(f"微代理 {best_agent_name} 处理失败: {e}")
                return None

        return None

    def _execute_api_call(self, api_call: Dict[str, Any]) -> Dict[str, Any]:
        """执行API调用"""
        try:
            endpoint = api_call.get("endpoint", "")
            parameters = api_call.get("parameters", {})

            # 根据endpoint调用相应的API
            if endpoint == "query_statistics_table" or "stats" in endpoint:
                return self._execute_stats_query({"parameters": parameters})
            elif endpoint == "get_config" or "config" in endpoint:
                return self._execute_config_query({"parameters": parameters})
            else:
                return {
                    "success": False,
                    "message": f"不支持的API端点: {endpoint}"
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "API调用执行失败"
            }

    async def _fallback_processing(self, query: str, triggered_agents: List[str]) -> Dict[str, Any]:
        """传统的fallback处理方法"""
        try:
            # 使用增强解析器
            query_intent = self.query_parser.parse_query(query)

            parsed_query = {
                "type": query_intent.intent_type,
                "parameters": query_intent.parameters
            }

            # 执行查询
            result = self._execute_query(parsed_query)

            return result

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Fallback处理失败"
            }
    
    def _generate_cache_key(self, query: str) -> str:
        """生成缓存键"""
        return hashlib.md5(query.encode('utf-8')).hexdigest()
    
    def _is_cache_valid(self, cached_result: Dict[str, Any]) -> bool:
        """检查缓存是否有效"""
        if "timestamp" not in cached_result:
            return False
        
        cache_time = datetime.fromisoformat(cached_result["timestamp"])
        # 缓存1小时有效
        return datetime.now() - cache_time < timedelta(hours=1)
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """缓存查询结果"""
        self.query_cache[cache_key] = {
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    
    def _execute_query(self, parsed_query: Dict[str, Any]) -> Dict[str, Any]:
        """执行解析后的查询"""
        global api_client

        if api_client is None:
            return {
                "success": False,
                "message": "MCP API未连接，请先调用setup_api_connection"
            }

        query_type = parsed_query.get("type")

        if query_type == "stats_query":
            return self._execute_stats_query(parsed_query)
        elif query_type == "config_query":
            return self._execute_config_query(parsed_query)
        elif query_type == "app_id_query":
            return self._execute_app_id_query(parsed_query)
        elif query_type == "netlink_list_query":
            return self._execute_netlink_list_query(parsed_query)
        else:
            return {
                "success": False,
                "message": f"不支持的查询类型: {query_type}"
            }
    
    def _execute_stats_query(self, parsed_query: Dict[str, Any]) -> Dict[str, Any]:
        """执行统计查询"""
        params = parsed_query.get("parameters", {})
        
        # 调用原始的统计查询功能
        result = api_client.query_stats_data(
            table=params.get("table", "session_stat"),
            begintime=params.get("begintime"),
            endtime=params.get("endtime"),
            fields=params.get("fields", ["total_byte"]),
            keys=params.get("keys", []),
            timeunit=params.get("timeunit", 0),
            filter_condition=params.get("filter", ""),
            topcount=params.get("topcount", 1000),
            sortfield=params.get("sortfield", "total_byte"),
            sorttype=params.get("sorttype", 2),
            netlink=params.get("netlink", 2)
        )
        
        return result
    
    def _execute_config_query(self, parsed_query: Dict[str, Any]) -> Dict[str, Any]:
        """执行配置查询"""
        params = parsed_query.get("parameters", {})

        # 调用配置查询功能
        config_type = params.get("config_type", "application")
        netlink_id = params.get("netlink_id", 1)

        # 这里需要调用get_config函数
        # 由于原始代码中的get_config是MCP工具，我们需要直接调用API
        return api_client.get_config(config_type, netlink_id)

    def _execute_app_id_query(self, parsed_query: Dict[str, Any]) -> Dict[str, Any]:
        """执行应用ID查询"""
        params = parsed_query.get("parameters", {})
        app_name = params.get("app_name", "")

        try:
            # 查询应用配置
            result = api_client.get_config("application", params.get("netlink_id", 1))

            if not result.get("success", False):
                return result

            # 解析XML配置查找应用ID
            config_data = result.get("data", "")
            app_id = self._parse_app_id_from_config(config_data, app_name)

            if app_id:
                return {
                    "success": True,
                    "message": f"找到应用 '{app_name}' 的ID",
                    "data": {
                        "app_name": app_name,
                        "app_id": app_id,
                        "query_type": "app_id_query"
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"未找到应用 '{app_name}'",
                    "data": None
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"查询应用ID失败: {str(e)}",
                "data": None
            }

    def _execute_netlink_list_query(self, parsed_query: Dict[str, Any]) -> Dict[str, Any]:
        """执行链路列表查询"""
        try:
            # 查询链路配置
            result = api_client.get_config("netlink", 64)

            if not result.get("success", False):
                return result

            # 解析XML配置获取链路列表
            config_data = result.get("data", "")
            netlinks = self._parse_netlinks_from_config(config_data)

            return {
                "success": True,
                "message": f"找到 {len(netlinks)} 个链路",
                "data": {
                    "netlinks": netlinks,
                    "total_count": len(netlinks),
                    "query_type": "netlink_list_query"
                }
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"查询链路列表失败: {str(e)}",
                "data": None
            }

    def _parse_app_id_from_config(self, config_data: str, app_name: str) -> Optional[int]:
        """从配置数据中解析应用ID"""
        try:
            import xml.etree.ElementTree as ET

            # 尝试解析XML
            root = ET.fromstring(config_data)

            # 查找应用配置
            for app in root.findall(".//app"):
                name = app.get("name", "")
                if name.lower() == app_name.lower():
                    return int(app.get("id", 0))

            # 模糊匹配
            for app in root.findall(".//app"):
                name = app.get("name", "")
                if app_name.lower() in name.lower() or name.lower() in app_name.lower():
                    return int(app.get("id", 0))

            return None

        except Exception as e:
            print(f"解析应用配置失败: {e}")
            return None

    def _parse_netlinks_from_config(self, config_data: str) -> List[Dict[str, Any]]:
        """从配置数据中解析链路列表"""
        try:
            import xml.etree.ElementTree as ET

            # 尝试解析XML
            root = ET.fromstring(config_data)
            netlinks = []

            # 查找链路配置
            for link in root.findall(".//netlink"):
                netlink_info = {
                    "id": int(link.get("id", 0)),
                    "name": link.get("name", f"链路{link.get('id', 0)}"),
                    "status": link.get("status", "unknown"),
                    "description": link.get("description", "")
                }
                netlinks.append(netlink_info)

            return netlinks

        except Exception as e:
            print(f"解析链路配置失败: {e}")
            return []
    
    def learn_pattern(self, query: str, result: str, feedback: str = "") -> str:
        """学习查询模式"""
        try:
            learned_patterns_file = self.knowledge_dir / "learned_patterns" / "user_patterns.json"
            
            # 加载现有模式
            if learned_patterns_file.exists():
                with open(learned_patterns_file, 'r', encoding='utf-8') as f:
                    learned_patterns = json.load(f)
            else:
                learned_patterns = {}
            
            # 添加新模式
            pattern_id = f"learned_{len(learned_patterns) + 1}"
            learned_patterns[pattern_id] = {
                "query": query,
                "result": result,
                "feedback": feedback,
                "timestamp": datetime.now().isoformat(),
                "usage_count": 1
            }
            
            # 保存模式
            with open(learned_patterns_file, 'w', encoding='utf-8') as f:
                json.dump(learned_patterns, f, ensure_ascii=False, indent=2)
            
            return f"成功学习查询模式: {pattern_id}"
            
        except Exception as e:
            return f"学习模式失败: {str(e)}"
    
    def run(self):
        """运行MCP服务器"""
        self.mcp.run()

    def process_intelligent_query_sync(self, query: str) -> str:
        """同步版本的智能查询处理"""
        import asyncio
        return asyncio.run(self.process_intelligent_query(query))


class SimpleKnowledgeAgent:
    """简化版知识代理（当Agent-KB不可用时使用）"""

    def __init__(self, name: str, triggers: List[str], patterns: List[str]):
        self.name = name
        self.triggers = triggers
        self.patterns = patterns

    def process_query(self, query: str) -> Dict[str, Any]:
        """处理查询（简化版本）"""
        return {
            "success": False,
            "message": f"简化版代理 {self.name} 无法处理复杂查询",
            "agent": self.name
        }


class QueryParser:
    """查询解析器 - 将自然语言转换为API调用"""
    
    def __init__(self, agent: MCPEnhancedAgent):
        self.agent = agent
    
    def parse(self, query: str, triggered_agents: List[str]) -> Dict[str, Any]:
        """
        解析自然语言查询

        Args:
            query: 用户查询
            triggered_agents: 触发的微代理列表

        Returns:
            解析后的查询参数
        """
        query_lower = query.lower()

        # 检测具体查询类型
        if "应用" in query_lower and ("id" in query_lower or "编号" in query_lower):
            return self._parse_app_id_query(query)
        elif "链路" in query_lower and ("哪些" in query_lower or "列表" in query_lower or "有什么" in query_lower):
            return self._parse_netlink_list_query(query)
        elif any(keyword in query_lower for keyword in ["链路", "统计", "流量", "概要", "top", "排行"]):
            return self._parse_stats_query(query)
        elif any(keyword in query_lower for keyword in ["配置", "设备", "协议"]):
            return self._parse_config_query(query)
        else:
            return self._parse_general_query(query)

    def _parse_app_id_query(self, query: str) -> Dict[str, Any]:
        """解析应用ID查询"""
        import re

        # 提取应用名称
        app_name = ""

        # 匹配 "应用xxxx的id" 模式
        match = re.search(r'应用([^\s的]+)', query)
        if match:
            app_name = match.group(1)

        # 提取链路ID
        netlink = self._extract_netlink(query)

        return {
            "type": "app_id_query",
            "parameters": {
                "app_name": app_name,
                "netlink_id": netlink
            }
        }

    def _parse_netlink_list_query(self, query: str) -> Dict[str, Any]:
        """解析链路列表查询"""
        return {
            "type": "netlink_list_query",
            "parameters": {}
        }
    
    def _parse_stats_query(self, query: str) -> Dict[str, Any]:
        """解析统计查询"""
        query_lower = query.lower()

        # 提取时间范围
        time_range = self._extract_time_range(query)

        # 提取链路ID
        netlink = self._extract_netlink(query)

        # 提取Top数量
        topcount = self._extract_topcount(query)

        # 根据查询内容确定字段和键
        fields = ["total_byte", "total_packet"]
        keys = ["server_ip_addr"]
        sortfield = "total_byte"

        # IP地址相关查询
        if "ip" in query_lower and ("top" in query_lower or "排行" in query_lower):
            fields = ["total_byte", "total_packet"]
            keys = ["server_ip_addr"]
            sortfield = "total_byte"
        # 会话统计查询
        elif "会话" in query_lower or "session" in query_lower:
            fields = ["session_count", "total_byte"]
            keys = ["server_ip_addr"]
            sortfield = "session_count"
        # 协议分析查询
        elif "协议" in query_lower:
            fields = ["total_byte", "total_packet"]
            keys = ["protocol"]
            sortfield = "total_byte"
        # 端口分析查询
        elif "端口" in query_lower:
            fields = ["total_byte", "session_count"]
            keys = ["server_port"]
            sortfield = "total_byte"
        # 概要统计查询
        elif "概要" in query_lower or "统计" in query_lower:
            fields = ["total_byte", "total_packet", "session_count"]
            keys = ["server_ip_addr"]
            sortfield = "total_byte"

        # 确定时间单位
        timeunit = 0
        if "小时" in query_lower or "hour" in query_lower:
            timeunit = 3600000
        elif "天" in query_lower or "日" in query_lower or "day" in query_lower:
            timeunit = 86400000
        elif "分钟" in query_lower or "minute" in query_lower:
            timeunit = 60000

        return {
            "type": "stats_query",
            "parameters": {
                "table": "session_stat",
                "begintime": time_range["begin"],
                "endtime": time_range["end"],
                "fields": fields,
                "keys": keys,
                "netlink": netlink,
                "topcount": topcount,
                "sortfield": sortfield,
                "sorttype": 2,
                "timeunit": timeunit,
                "filter_condition": ""
            }
        }
    
    def _parse_config_query(self, query: str) -> Dict[str, Any]:
        """解析配置查询"""
        return {
            "type": "config_query", 
            "parameters": {
                "config_type": "application",
                "netlink_id": self._extract_netlink(query)
            }
        }
    
    def _parse_general_query(self, query: str) -> Dict[str, Any]:
        """解析通用查询"""
        return {
            "type": "general_query",
            "parameters": {
                "query": query
            }
        }
    
    def _extract_time_range(self, query: str) -> Dict[str, str]:
        """提取时间范围"""
        if "今天" in query:
            today = datetime.now().strftime("%Y-%m-%d")
            return {
                "begin": f"{today} 00:00:00",
                "end": f"{today} 23:59:59"
            }
        elif "昨天" in query:
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            return {
                "begin": f"{yesterday} 00:00:00", 
                "end": f"{yesterday} 23:59:59"
            }
        else:
            # 默认今天
            today = datetime.now().strftime("%Y-%m-%d")
            return {
                "begin": f"{today} 00:00:00",
                "end": f"{today} 23:59:59"
            }
    
    def _extract_netlink(self, query: str) -> int:
        """提取链路ID"""
        import re
        match = re.search(r'链路(\d+)', query)
        if match:
            return int(match.group(1))
        return 2  # 默认链路2
    
    def _extract_topcount(self, query: str) -> int:
        """提取Top数量"""
        import re
        match = re.search(r'top(\d+)', query.lower())
        if match:
            return int(match.group(1))
        return 1000  # 默认1000


if __name__ == '__main__':
    # 创建并运行MCP增强代理
    agent = MCPEnhancedAgent()
    agent.run()
