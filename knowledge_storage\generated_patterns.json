{"006ce6d4536b8cc1": {"pattern_id": "006ce6d4536b8cc1", "name": "unknown_pattern", "description": "Gemini生成的知识模式", "intent_type": "general_query", "keywords": [], "parameters": {}, "examples": ["查询下应用HTTP的id是多少"], "api_mapping": {}, "confidence": 0.8, "source_model": "gemini", "created_at": "2025-07-16T17:04:24.105836", "last_used": "2025-07-16T17:14:39.218878", "usage_count": 1, "success_count": 0}, "42994a881162cff8": {"pattern_id": "42994a881162cff8", "name": "unknown_pattern", "description": "Gemini生成的知识模式", "intent_type": "general_query", "keywords": [], "parameters": {}, "examples": ["查询下链路2下今天的ip地址Top20"], "api_mapping": {}, "confidence": 0.8, "source_model": "gemini", "created_at": "2025-07-16T17:12:58.879273", "last_used": "2025-07-16T17:14:59.751965", "usage_count": 1, "success_count": 0}, "9d8389135c9fc62b": {"pattern_id": "9d8389135c9fc62b", "name": "unknown_pattern", "description": "Gemini生成的知识模式", "intent_type": "general_query", "keywords": [], "parameters": {}, "examples": ["查询下当前有哪些链路"], "api_mapping": {}, "confidence": 0.8, "source_model": "gemini", "created_at": "2025-07-16T17:13:19.566664", "last_used": "2025-07-16T17:14:16.326328", "usage_count": 1, "success_count": 0}, "cbfeacfa967a49b1": {"pattern_id": "cbfeacfa967a49b1", "name": "unknown_pattern", "description": "Gemini生成的知识模式", "intent_type": "general_query", "keywords": [], "parameters": {}, "examples": ["查询链路1下今天的概要统计"], "api_mapping": {}, "confidence": 0.8, "source_model": "gemini", "created_at": "2025-07-16T17:13:38.148308", "last_used": "2025-07-16T17:13:49.897030", "usage_count": 1, "success_count": 0}, "f085c847ef9ce119": {"pattern_id": "f085c847ef9ce119", "name": "unknown_pattern", "description": "Gemini生成的知识模式", "intent_type": "general_query", "keywords": [], "parameters": {}, "examples": ["查询链路1下今天的概要统计"], "api_mapping": {}, "confidence": 0.8, "source_model": "gemini", "created_at": "2025-07-16T17:14:04.368778", "last_used": "2025-07-16T17:14:04.368778", "usage_count": 0, "success_count": 0}, "a92f303b54a34943": {"pattern_id": "a92f303b54a34943", "name": "unknown_pattern", "description": "Gemini生成的知识模式", "intent_type": "general_query", "keywords": [], "parameters": {}, "examples": ["查询下当前有哪些链路"], "api_mapping": {}, "confidence": 0.8, "source_model": "gemini", "created_at": "2025-07-16T17:14:28.464144", "last_used": "2025-07-16T17:14:28.464144", "usage_count": 0, "success_count": 0}, "e4dfd4846947bfb7": {"pattern_id": "e4dfd4846947bfb7", "name": "ProtocolIDLookup", "description": "查询特定应用或协议的唯一标识符（ID）。", "intent_type": "ProtocolIDLookup", "keywords": ["查询", "应用", "HTTP", "ID", "协议"], "parameters": {"table": "app_protocol_info", "fields": ["protocol_id", "protocol_name"], "keys": ["protocol_name"], "extraction_rules": {"protocol_name": {"pattern": "(?<=应用|协议)([^的]+)(?=的id|id是多少|的编号)", "example": "HTTP"}, "netlink_id": {"pattern": "(?<=链路ID为|链路|ID是)(\\d+)", "example": "100", "description": "用于查询特定链路的统计信息，本查询未包含。"}, "time_range": {"pattern": "(最近\\d+小时|今天|昨天|近一周)", "example": "最近一小时", "description": "用于查询特定时间范围的数据，本查询未包含。"}, "topcount": {"pattern": "(Top\\s*\\d+|前\\s*\\d+)", "example": "Top 10", "description": "用于查询Top N数据，本查询未包含。"}}}, "examples": ["查询下HTTPS的ID是多少", "FTP协议的编号是多少", "获取SSH应用的id", "查找DNS的ID"], "api_mapping": {"endpoint": "/api/v1/network_stats/protocols", "method": "GET", "required_params": [{"name": "filter", "value_map": {"protocol_name": "{extracted_protocol_name}"}}, {"name": "fields", "value": "protocol_id,protocol_name"}]}, "confidence": 0.8, "source_model": "gemini", "created_at": "2025-07-16T17:14:49.149208", "last_used": "2025-07-16T17:14:49.149208", "usage_count": 0, "success_count": 0}, "86879f2a40b7de20": {"pattern_id": "86879f2a40b7de20", "name": "unknown_pattern", "description": "Gemini生成的知识模式", "intent_type": "general_query", "keywords": [], "parameters": {}, "examples": ["查询下链路2下今天的ip地址Top20"], "api_mapping": {}, "confidence": 0.8, "source_model": "gemini", "created_at": "2025-07-16T17:15:11.070449", "last_used": "2025-07-16T17:15:11.070449", "usage_count": 0, "success_count": 0}, "f36e16a801a66a42": {"pattern_id": "f36e16a801a66a42", "name": "unknown_pattern", "description": "Gemini生成的知识模式", "intent_type": "general_query", "keywords": [], "parameters": {}, "examples": ["分析链路1的协议分布"], "api_mapping": {}, "confidence": 0.8, "source_model": "gemini", "created_at": "2025-07-16T17:15:28.172501", "last_used": "2025-07-16T17:15:28.172501", "usage_count": 0, "success_count": 0}}