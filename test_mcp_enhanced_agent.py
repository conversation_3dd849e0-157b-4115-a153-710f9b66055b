#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP Enhanced Agent 测试脚本
测试各种查询场景和功能模块
"""

import json
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟Agent-KB组件（如果不可用）
class MockBaseMicroAgent:
    pass

class MockKnowledgeMicroAgent:
    def __init__(self, name, triggers, patterns):
        self.name = name
        self.triggers = triggers
        self.patterns = patterns

class MockRepoMicroAgent:
    pass

# 设置模拟对象
sys.modules['openhands.microagent'] = Mock()
sys.modules['openhands.memory.memory'] = Mock()
sys.modules['openhands.events.stream'] = Mock()
sys.modules['openhands.events.action.agent'] = Mock()
sys.modules['openhands.events.event'] = Mock()

# 模拟MCP服务器
sys.modules['mcp.server.fastmcp'] = Mock()

# 模拟原始MCP服务器
class MockStatsApiClient:
    def query_stats_data(self, **kwargs):
        return {
            "success": True,
            "data": "mock_csv_data",
            "message": "查询成功"
        }
    
    def get_config(self, config_type, netlink_id):
        if config_type == "application":
            return {
                "success": True,
                "data": '<application><app id="1" name="HTTP" port="80"/></application>'
            }
        elif config_type == "netlink":
            return {
                "success": True,
                "data": '<netlink><link id="1" name="链路1"/><link id="2" name="链路2"/></netlink>'
            }
        return {"success": False, "message": "配置类型不支持"}

# 设置模拟的API客户端
mock_api_client = MockStatsApiClient()

# 导入要测试的模块
try:
    from mcp_enhanced_agent import MCPEnhancedAgent, SimpleKnowledgeAgent
    from enhanced_query_parser import EnhancedQueryParser
    from knowledge_management_system import KnowledgeManagementSystem, QueryPattern
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必要的文件都存在")
    sys.exit(1)

class TestMCPEnhancedAgent(unittest.TestCase):
    """测试MCP增强代理"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时配置目录
        self.test_config_dir = "test_mcp_agent_config"
        
        # 模拟API客户端
        with patch('mcp_enhanced_agent.api_client', mock_api_client):
            self.agent = MCPEnhancedAgent(self.test_config_dir)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        import time
        if os.path.exists(self.test_config_dir):
            # 尝试多次删除，处理Windows文件锁定问题
            for attempt in range(3):
                try:
                    shutil.rmtree(self.test_config_dir)
                    break
                except (OSError, PermissionError) as e:
                    if attempt < 2:
                        time.sleep(0.1)
                        continue
                    print(f"警告: 无法完全清理测试目录 {self.test_config_dir}: {e}")
    
    def test_microagent_loading(self):
        """测试微代理加载"""
        self.assertIsInstance(self.agent.knowledge_microagents, dict)
        self.assertGreater(len(self.agent.knowledge_microagents), 0)
        
        # 检查默认微代理
        self.assertIn("stats_query", self.agent.knowledge_microagents)
        self.assertIn("network_analysis", self.agent.knowledge_microagents)
        self.assertIn("ip_analysis", self.agent.knowledge_microagents)
    
    def test_microagent_triggering(self):
        """测试微代理触发"""
        # 测试统计查询触发
        triggered = self.agent._trigger_microagents("查询链路1的统计数据")
        self.assertIn("stats_query", triggered)
        
        # 测试应用查询触发
        triggered = self.agent._trigger_microagents("查询应用HTTP的id")
        self.assertIn("network_analysis", triggered)
        
        # 测试IP分析触发
        triggered = self.agent._trigger_microagents("查询IP地址Top20")
        self.assertIn("ip_analysis", triggered)
    
    @patch('mcp_enhanced_agent.api_client', mock_api_client)
    def test_app_id_query(self):
        """测试应用ID查询"""
        query = "查询下应用HTTP的id是多少"
        result_json = self.agent.process_intelligent_query_sync(query)
        result = json.loads(result_json)

        # 检查结果结构（可能成功或失败，但应该有正确的结构）
        self.assertIsInstance(result, dict)
        self.assertIn("success", result)

    @patch('mcp_enhanced_agent.api_client', mock_api_client)
    def test_netlink_list_query(self):
        """测试链路列表查询"""
        query = "查询下当前有哪些链路"
        result_json = self.agent.process_intelligent_query_sync(query)
        result = json.loads(result_json)

        # 检查结果结构（可能成功或失败，但应该有正确的结构）
        self.assertIsInstance(result, dict)
        self.assertIn("success", result)

    @patch('mcp_enhanced_agent.api_client', mock_api_client)
    def test_stats_query(self):
        """测试统计查询"""
        query = "查询链路1下今天的概要统计"
        result_json = self.agent.process_intelligent_query_sync(query)
        result = json.loads(result_json)

        # 检查结果结构（可能成功或失败，但应该有正确的结构）
        self.assertIsInstance(result, dict)
        self.assertIn("success", result)

    @patch('mcp_enhanced_agent.api_client', mock_api_client)
    def test_ip_top_query(self):
        """测试IP Top查询"""
        query = "查询下链路2下今天的ip地址Top20"
        result_json = self.agent.process_intelligent_query_sync(query)
        result = json.loads(result_json)

        # 检查结果结构（可能成功或失败，但应该有正确的结构）
        self.assertIsInstance(result, dict)
        self.assertIn("success", result)

class TestEnhancedQueryParser(unittest.TestCase):
    """测试增强查询解析器"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_agent = Mock()
        self.parser = EnhancedQueryParser(self.mock_agent)
    
    def test_netlink_id_extraction(self):
        """测试链路ID提取"""
        # 测试明确的链路ID
        netlink_id = self.parser._extract_netlink_id("查询链路1的统计")
        self.assertEqual(netlink_id, 1)
        
        netlink_id = self.parser._extract_netlink_id("链路2下的数据")
        self.assertEqual(netlink_id, 2)
        
        # 测试无链路ID的情况
        netlink_id = self.parser._extract_netlink_id("查询统计数据")
        self.assertIsNone(netlink_id)
    
    def test_app_name_extraction(self):
        """测试应用名称提取"""
        app_name = self.parser._extract_app_name("查询应用HTTP的id")
        self.assertEqual(app_name, "HTTP")
        
        app_name = self.parser._extract_app_name("应用FTP的配置")
        self.assertEqual(app_name, "FTP")
        
        app_name = self.parser._extract_app_name("查询配置信息")
        self.assertIsNone(app_name)
    
    def test_topcount_extraction(self):
        """测试Top数量提取"""
        topcount = self.parser._extract_topcount("查询Top20的IP地址")
        self.assertEqual(topcount, 20)
        
        topcount = self.parser._extract_topcount("前十名的统计")
        self.assertEqual(topcount, 10)
        
        topcount = self.parser._extract_topcount("查询统计数据")
        self.assertIsNone(topcount)
    
    def test_time_range_extraction(self):
        """测试时间范围提取"""
        # 测试今天
        time_range = self.parser._extract_time_range("查询今天的数据")
        self.assertIsNotNone(time_range)
        self.assertIn("begin", time_range)
        self.assertIn("end", time_range)
        
        # 测试昨天
        time_range = self.parser._extract_time_range("查询昨天的统计")
        self.assertIsNotNone(time_range)
        
        # 测试具体日期
        time_range = self.parser._extract_time_range("查询2024-01-15的数据")
        self.assertIsNotNone(time_range)
        self.assertIn("2024-01-15", time_range["begin"])
    
    def test_intent_identification(self):
        """测试意图识别"""
        # 测试统计查询意图
        intent = self.parser._identify_intent("查询链路1的统计数据")
        self.assertEqual(intent["intent_type"], "stats_query")
        
        # 测试IP Top查询意图
        intent = self.parser._identify_intent("查询IP地址Top20")
        self.assertEqual(intent["intent_type"], "ip_top_query")
        
        # 测试应用ID查询意图
        intent = self.parser._identify_intent("查询应用HTTP的id")
        self.assertEqual(intent["intent_type"], "app_id_query")
        
        # 测试链路列表查询意图
        intent = self.parser._identify_intent("查询有哪些链路")
        self.assertEqual(intent["intent_type"], "netlink_list_query")

class TestKnowledgeManagementSystem(unittest.TestCase):
    """测试知识管理系统"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config_dir = "test_knowledge_config"
        self.knowledge_system = KnowledgeManagementSystem(self.test_config_dir)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        import time
        if os.path.exists(self.test_config_dir):
            # 尝试多次删除，处理Windows文件锁定问题
            for attempt in range(3):
                try:
                    shutil.rmtree(self.test_config_dir)
                    break
                except (OSError, PermissionError) as e:
                    if attempt < 2:
                        time.sleep(0.1)
                        continue
                    print(f"警告: 无法完全清理测试目录 {self.test_config_dir}: {e}")
    
    def test_pattern_learning(self):
        """测试模式学习"""
        query = "查询链路1的统计数据"
        result = {"success": True, "data": "test_data"}
        
        learn_result = self.knowledge_system.learn_pattern(query, result, "test")
        self.assertIn("学习", learn_result)
        
        # 检查模式是否被保存
        pattern_id = self.knowledge_system._generate_pattern_id(query)
        self.assertIn(pattern_id, self.knowledge_system.query_patterns)
    
    def test_similar_pattern_finding(self):
        """测试相似模式查找"""
        # 先学习一个模式
        query1 = "查询链路1的统计数据"
        result1 = {"success": True, "data": "test_data"}
        self.knowledge_system.learn_pattern(query1, result1, "test")
        
        # 查找相似模式
        similar_patterns = self.knowledge_system.find_similar_patterns("查询链路2的统计信息")
        self.assertGreater(len(similar_patterns), 0)
    
    def test_model_selection(self):
        """测试模型选择"""
        # 测试复杂查询选择Gemini
        complex_query = "分析链路1的异常流量趋势"
        model = self.knowledge_system.get_best_model_for_query(complex_query)
        self.assertEqual(model, "gemini")
        
        # 测试简单查询选择Qwen3
        simple_query = "查询链路1的统计"
        model = self.knowledge_system.get_best_model_for_query(simple_query)
        self.assertEqual(model, "qwen3")
    
    def test_caching(self):
        """测试缓存功能"""
        model_name = "test_model"
        query = "test_query"
        result = {"test": "data"}
        
        # 缓存结果
        self.knowledge_system.cache_model_result(model_name, query, result)
        
        # 获取缓存结果
        cached_result = self.knowledge_system.get_cached_result(model_name, query)
        self.assertEqual(cached_result, result)
    
    def test_model_stats_update(self):
        """测试模型统计更新"""
        model_name = "test_model"
        
        # 更新统计
        self.knowledge_system.update_model_stats(model_name, True, 1.5)
        
        # 检查统计是否更新
        self.assertIn(model_name, self.knowledge_system.model_stats)
        stats = self.knowledge_system.model_stats[model_name]
        self.assertEqual(stats.total_queries, 1)
        self.assertEqual(stats.success_queries, 1)
        self.assertEqual(stats.avg_response_time, 1.5)

def run_integration_tests():
    """运行集成测试"""
    print("🧪 开始集成测试...")
    
    # 测试查询场景
    test_queries = [
        "查询链路1下今天的概要统计",
        "查询下当前有哪些链路", 
        "查询下应用HTTP的id是多少",
        "查询下链路2下今天的ip地址Top20",
        "分析链路1的协议分布",
        "统计链路2今天的会话数量"
    ]
    
    print(f"📝 测试 {len(test_queries)} 个查询场景:")
    
    with patch('mcp_enhanced_agent.api_client', mock_api_client):
        agent = MCPEnhancedAgent("test_integration_config")

        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. 测试查询: {query}")

            try:
                result_json = agent.process_intelligent_query_sync(query)
                result = json.loads(result_json)
                
                if result.get("success", False):
                    print(f"   ✅ 成功")
                    if "meta" in result:
                        meta = result["meta"]
                        print(f"   📊 模型: {meta.get('model_used', 'unknown')}")
                        print(f"   ⏱️  响应时间: {meta.get('response_time', 0):.3f}s")
                        print(f"   🤖 触发代理: {meta.get('triggered_agents', [])}")
                else:
                    print(f"   ❌ 失败: {result.get('message', '未知错误')}")
                    
            except Exception as e:
                print(f"   💥 异常: {str(e)}")
    
    # 清理测试配置
    import shutil
    if os.path.exists("test_integration_config"):
        shutil.rmtree("test_integration_config")
    
    print("\n✅ 集成测试完成!")

def main():
    """主函数"""
    print("🚀 MCP Enhanced Agent 测试套件")
    print("=" * 50)
    
    # 运行单元测试
    print("\n📋 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行集成测试
    print("\n" + "=" * 50)
    run_integration_tests()
    
    print("\n🎉 所有测试完成!")

if __name__ == "__main__":
    main()
