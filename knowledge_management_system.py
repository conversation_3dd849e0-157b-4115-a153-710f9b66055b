#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识管理系统 - 支持动态配置加载、模式学习和多模型集成
基于Agent-KB架构，实现智能化的知识库管理和查询优化
"""

import json
import os
import hashlib
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict
import threading
import time

@dataclass
class QueryPattern:
    """查询模式数据类"""
    pattern_id: str
    query_text: str
    intent_type: str
    parameters: Dict[str, Any]
    success_rate: float
    usage_count: int
    last_used: datetime
    created_at: datetime
    model_source: str  # "gemini", "qwen3", "user"

@dataclass
class ModelUsageStats:
    """模型使用统计"""
    model_name: str
    total_queries: int
    success_queries: int
    avg_response_time: float
    last_used: datetime

class KnowledgeManagementSystem:
    """知识管理系统"""
    
    def __init__(self, config_dir: str = "mcp_agent_config"):
        self.config_dir = Path(config_dir)
        self.knowledge_dir = self.config_dir / "knowledge_base"
        self.learned_patterns_dir = self.knowledge_dir / "learned_patterns"
        self.model_cache_dir = self.knowledge_dir / "model_cache"
        
        # 确保目录存在
        self._ensure_directories()
        
        # 初始化数据结构
        self.query_patterns: Dict[str, QueryPattern] = {}
        self.model_stats: Dict[str, ModelUsageStats] = {}
        self.pattern_index: Dict[str, List[str]] = {}  # 关键词到模式ID的索引
        self.cache: Dict[str, Any] = {}
        
        # 配置参数
        self.max_cache_size = 1000
        self.pattern_cleanup_threshold = 100  # 清理低使用率模式的阈值
        self.auto_save_interval = 300  # 自动保存间隔（秒）
        
        # 加载现有数据
        self._load_all_data()
        
        # 启动后台任务
        self._start_background_tasks()
    
    def _ensure_directories(self):
        """确保必要目录存在"""
        directories = [
            self.learned_patterns_dir,
            self.model_cache_dir,
            self.model_cache_dir / "gemini",
            self.model_cache_dir / "qwen3"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _load_all_data(self):
        """加载所有数据"""
        self._load_query_patterns()
        self._load_model_stats()
        self._build_pattern_index()
    
    def _load_query_patterns(self):
        """加载查询模式"""
        patterns_file = self.learned_patterns_dir / "query_patterns.json"
        
        if patterns_file.exists():
            try:
                with open(patterns_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for pattern_id, pattern_data in data.items():
                    # 转换日期字符串为datetime对象
                    pattern_data["last_used"] = datetime.fromisoformat(pattern_data["last_used"])
                    pattern_data["created_at"] = datetime.fromisoformat(pattern_data["created_at"])
                    
                    self.query_patterns[pattern_id] = QueryPattern(**pattern_data)
                    
                print(f"加载了 {len(self.query_patterns)} 个查询模式")
                
            except Exception as e:
                print(f"加载查询模式失败: {e}")
    
    def _load_model_stats(self):
        """加载模型统计"""
        stats_file = self.learned_patterns_dir / "model_stats.json"
        
        if stats_file.exists():
            try:
                with open(stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for model_name, stats_data in data.items():
                    stats_data["last_used"] = datetime.fromisoformat(stats_data["last_used"])
                    self.model_stats[model_name] = ModelUsageStats(**stats_data)
                    
                print(f"加载了 {len(self.model_stats)} 个模型统计")
                
            except Exception as e:
                print(f"加载模型统计失败: {e}")
    
    def _build_pattern_index(self):
        """构建模式索引"""
        self.pattern_index.clear()
        
        for pattern_id, pattern in self.query_patterns.items():
            # 提取关键词
            keywords = self._extract_keywords(pattern.query_text)
            
            for keyword in keywords:
                if keyword not in self.pattern_index:
                    self.pattern_index[keyword] = []
                self.pattern_index[keyword].append(pattern_id)
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        import re
        
        # 简单的关键词提取
        keywords = []
        
        # 中文关键词
        chinese_keywords = re.findall(r'[\u4e00-\u9fff]+', text)
        keywords.extend(chinese_keywords)
        
        # 英文关键词
        english_keywords = re.findall(r'[a-zA-Z]+', text.lower())
        keywords.extend(english_keywords)
        
        # 数字
        numbers = re.findall(r'\d+', text)
        keywords.extend(numbers)
        
        return list(set(keywords))
    
    def learn_pattern(self, query: str, result: Dict[str, Any], 
                     model_source: str = "user", feedback: str = "") -> str:
        """学习新的查询模式"""
        try:
            # 生成模式ID
            pattern_id = self._generate_pattern_id(query)
            
            # 检查是否已存在
            if pattern_id in self.query_patterns:
                # 更新现有模式
                pattern = self.query_patterns[pattern_id]
                pattern.usage_count += 1
                pattern.last_used = datetime.now()
                
                # 更新成功率
                if result.get("success", False):
                    pattern.success_rate = (pattern.success_rate * (pattern.usage_count - 1) + 1.0) / pattern.usage_count
                else:
                    pattern.success_rate = (pattern.success_rate * (pattern.usage_count - 1)) / pattern.usage_count
                
                return f"更新现有模式: {pattern_id}"
            
            # 创建新模式
            intent_type = self._infer_intent_type(query, result)
            parameters = self._extract_parameters_from_result(result)
            
            new_pattern = QueryPattern(
                pattern_id=pattern_id,
                query_text=query,
                intent_type=intent_type,
                parameters=parameters,
                success_rate=1.0 if result.get("success", False) else 0.0,
                usage_count=1,
                last_used=datetime.now(),
                created_at=datetime.now(),
                model_source=model_source
            )
            
            self.query_patterns[pattern_id] = new_pattern
            
            # 更新索引
            keywords = self._extract_keywords(query)
            for keyword in keywords:
                if keyword not in self.pattern_index:
                    self.pattern_index[keyword] = []
                self.pattern_index[keyword].append(pattern_id)
            
            # 保存到文件
            self._save_query_patterns()
            
            return f"学习新模式: {pattern_id}"
            
        except Exception as e:
            return f"学习模式失败: {str(e)}"
    
    def _generate_pattern_id(self, query: str) -> str:
        """生成模式ID"""
        return hashlib.md5(query.encode('utf-8')).hexdigest()[:16]
    
    def _infer_intent_type(self, query: str, result: Dict[str, Any]) -> str:
        """推断查询意图类型"""
        query_lower = query.lower()
        
        if "ip" in query_lower and "top" in query_lower:
            return "ip_top_query"
        elif "应用" in query_lower and "id" in query_lower:
            return "app_id_query"
        elif "链路" in query_lower and ("哪些" in query_lower or "列表" in query_lower):
            return "netlink_list_query"
        elif any(keyword in query_lower for keyword in ["统计", "流量", "概要"]):
            return "stats_query"
        else:
            return "general_query"
    
    def _extract_parameters_from_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """从结果中提取参数"""
        parameters = {}
        
        if "query_info" in result:
            query_info = result["query_info"]
            parameters.update(query_info)
        
        return parameters
    
    def find_similar_patterns(self, query: str, limit: int = 5) -> List[QueryPattern]:
        """查找相似的查询模式"""
        keywords = self._extract_keywords(query)
        pattern_scores = {}
        
        # 基于关键词匹配计算相似度
        for keyword in keywords:
            if keyword in self.pattern_index:
                for pattern_id in self.pattern_index[keyword]:
                    if pattern_id not in pattern_scores:
                        pattern_scores[pattern_id] = 0
                    pattern_scores[pattern_id] += 1
        
        # 按相似度排序
        sorted_patterns = sorted(
            pattern_scores.items(),
            key=lambda x: (x[1], self.query_patterns[x[0]].success_rate, self.query_patterns[x[0]].usage_count),
            reverse=True
        )
        
        # 返回最相似的模式
        similar_patterns = []
        for pattern_id, score in sorted_patterns[:limit]:
            similar_patterns.append(self.query_patterns[pattern_id])
        
        return similar_patterns
    
    def get_best_model_for_query(self, query: str) -> str:
        """为查询选择最佳模型"""
        # 查找相似模式
        similar_patterns = self.find_similar_patterns(query, limit=3)
        
        if similar_patterns:
            # 如果有相似模式且成功率高，使用Qwen3
            best_pattern = similar_patterns[0]
            if best_pattern.success_rate > 0.8 and best_pattern.usage_count > 2:
                return "qwen3"
        
        # 复杂查询或新查询使用Gemini
        complexity_indicators = ["分析", "对比", "预测", "异常", "趋势"]
        if any(indicator in query for indicator in complexity_indicators):
            return "gemini"
        
        # 默认使用Qwen3
        return "qwen3"
    
    def cache_model_result(self, model_name: str, query: str, result: Any):
        """缓存模型结果"""
        cache_key = f"{model_name}:{hashlib.md5(query.encode()).hexdigest()}"

        cache_data = {
            "result": result,
            "timestamp": datetime.now().isoformat(),
            "model": model_name
        }

        # 确保模型特定的缓存目录存在
        model_cache_dir = self.model_cache_dir / model_name
        model_cache_dir.mkdir(parents=True, exist_ok=True)

        # 保存到文件缓存
        cache_file = model_cache_dir / f"{cache_key}.pkl"
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
        except Exception as e:
            print(f"缓存结果失败: {e}")
    
    def get_cached_result(self, model_name: str, query: str, max_age_hours: int = 24) -> Optional[Any]:
        """获取缓存的结果"""
        cache_key = f"{model_name}:{hashlib.md5(query.encode()).hexdigest()}"
        cache_file = self.model_cache_dir / model_name / f"{cache_key}.pkl"
        
        if not cache_file.exists():
            return None
        
        try:
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            # 检查缓存是否过期
            cache_time = datetime.fromisoformat(cache_data["timestamp"])
            if datetime.now() - cache_time > timedelta(hours=max_age_hours):
                return None
            
            return cache_data["result"]
            
        except Exception as e:
            print(f"读取缓存失败: {e}")
            return None
    
    def update_model_stats(self, model_name: str, success: bool, response_time: float):
        """更新模型统计"""
        if model_name not in self.model_stats:
            self.model_stats[model_name] = ModelUsageStats(
                model_name=model_name,
                total_queries=0,
                success_queries=0,
                avg_response_time=0.0,
                last_used=datetime.now()
            )
        
        stats = self.model_stats[model_name]
        stats.total_queries += 1
        if success:
            stats.success_queries += 1
        
        # 更新平均响应时间
        stats.avg_response_time = (
            (stats.avg_response_time * (stats.total_queries - 1) + response_time) / 
            stats.total_queries
        )
        stats.last_used = datetime.now()
        
        # 保存统计
        self._save_model_stats()
    
    def cleanup_old_patterns(self):
        """清理旧的低使用率模式"""
        current_time = datetime.now()
        patterns_to_remove = []
        
        for pattern_id, pattern in self.query_patterns.items():
            # 删除30天未使用且使用次数少于3次的模式
            if (current_time - pattern.last_used).days > 30 and pattern.usage_count < 3:
                patterns_to_remove.append(pattern_id)
        
        for pattern_id in patterns_to_remove:
            del self.query_patterns[pattern_id]
            
            # 从索引中移除
            for keyword_list in self.pattern_index.values():
                if pattern_id in keyword_list:
                    keyword_list.remove(pattern_id)
        
        if patterns_to_remove:
            print(f"清理了 {len(patterns_to_remove)} 个旧模式")
            self._save_query_patterns()
    
    def _save_query_patterns(self):
        """保存查询模式"""
        patterns_file = self.learned_patterns_dir / "query_patterns.json"
        
        try:
            # 转换为可序列化的格式
            serializable_patterns = {}
            for pattern_id, pattern in self.query_patterns.items():
                pattern_dict = asdict(pattern)
                pattern_dict["last_used"] = pattern.last_used.isoformat()
                pattern_dict["created_at"] = pattern.created_at.isoformat()
                serializable_patterns[pattern_id] = pattern_dict
            
            with open(patterns_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_patterns, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存查询模式失败: {e}")
    
    def _save_model_stats(self):
        """保存模型统计"""
        stats_file = self.learned_patterns_dir / "model_stats.json"
        
        try:
            serializable_stats = {}
            for model_name, stats in self.model_stats.items():
                stats_dict = asdict(stats)
                stats_dict["last_used"] = stats.last_used.isoformat()
                serializable_stats[model_name] = stats_dict
            
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_stats, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存模型统计失败: {e}")
    
    def _start_background_tasks(self):
        """启动后台任务"""
        def background_worker():
            while True:
                try:
                    # 自动保存
                    self._save_query_patterns()
                    self._save_model_stats()
                    
                    # 定期清理
                    if datetime.now().hour == 2:  # 凌晨2点清理
                        self.cleanup_old_patterns()
                    
                    time.sleep(self.auto_save_interval)
                    
                except Exception as e:
                    print(f"后台任务错误: {e}")
                    time.sleep(60)  # 出错后等待1分钟再继续
        
        # 启动后台线程
        background_thread = threading.Thread(target=background_worker, daemon=True)
        background_thread.start()
    
    def get_knowledge_summary(self) -> Dict[str, Any]:
        """获取知识库摘要"""
        total_patterns = len(self.query_patterns)
        successful_patterns = sum(1 for p in self.query_patterns.values() if p.success_rate > 0.8)
        
        model_summary = {}
        for model_name, stats in self.model_stats.items():
            model_summary[model_name] = {
                "total_queries": stats.total_queries,
                "success_rate": stats.success_queries / stats.total_queries if stats.total_queries > 0 else 0,
                "avg_response_time": stats.avg_response_time
            }
        
        return {
            "total_patterns": total_patterns,
            "successful_patterns": successful_patterns,
            "success_rate": successful_patterns / total_patterns if total_patterns > 0 else 0,
            "model_stats": model_summary,
            "last_updated": datetime.now().isoformat()
        }
